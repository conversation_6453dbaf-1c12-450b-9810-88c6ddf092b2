#!/usr/bin/env python3
"""
Full-Featured Web Server for OpenManus
A complete FastAPI server that integrates with the OpenManus agent.
"""

import argparse
import asyncio
import json
import logging
import traceback
from typing import Dict, Any, Optional

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse
    from pydantic import BaseModel
    import uvicorn
except ImportError:
    print("FastAPI and uvicorn are required. Please install them:")
    print("pip install fastapi uvicorn")
    exit(1)

# Try to import OpenManus components
try:
    from app.config import config
    from app.logger import logger as app_logger
    OPENMANUS_AVAILABLE = True
except ImportError as e:
    print(f"OpenManus components not fully available: {e}")
    OPENMANUS_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    status: str = "success"
    error: Optional[str] = None

# Create FastAPI app
app = FastAPI(
    title="OpenManus Full Web Interface",
    description="A complete web interface for the OpenManus AI agent with task execution capabilities",
    version="2.0.0"
)

# Simple in-memory session storage
sessions: Dict[str, Dict[str, Any]] = {}

async def execute_with_manus(message: str, session_id: str) -> str:
    """Execute a task using the OpenManus agent."""
    try:
        # Import here to avoid issues if dependencies are missing
        from app.agent.manus import Manus
        
        # Create Manus agent
        agent = await Manus.create()
        
        try:
            # Execute the task
            logger.info(f"Executing task with Manus: {message}")
            result = await agent.run(message)
            
            # The result might be None, so we need to handle that
            if result is None:
                return "Task completed successfully, but no specific output was returned."
            
            return str(result)
            
        finally:
            # Clean up agent resources
            await agent.cleanup()
            
    except Exception as e:
        error_msg = f"Error executing task: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return f"Sorry, I encountered an error while processing your request: {str(e)}"

async def execute_simple_response(message: str, session_id: str) -> str:
    """Provide a simple response when OpenManus is not available."""
    # Simple task simulation
    if "hello" in message.lower():
        return "Hello! I'm OpenManus. I can help you with various tasks, but I need full dependencies to be installed for complete functionality."
    elif "help" in message.lower():
        return """I can help you with:
- Code analysis and generation
- Web browsing and data extraction  
- File operations and text editing
- System commands execution
- Data analysis and visualization

Note: Full capabilities require all dependencies to be installed."""
    elif "time" in message.lower():
        import datetime
        return f"Current time is: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    else:
        return f"I received your message: '{message}'. To enable full task execution capabilities, please ensure all OpenManus dependencies are installed and configured properly."

@app.get("/", response_class=HTMLResponse)
async def home():
    """Serve the main web interface."""
    status_text = "Full OpenManus Integration" if OPENMANUS_AVAILABLE else "Limited Mode (Missing Dependencies)"
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>OpenManus Full Web Interface</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                max-width: 1000px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            h1 {{
                color: #333;
                text-align: center;
                margin-bottom: 10px;
            }}
            .status {{
                text-align: center;
                margin-bottom: 20px;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }}
            .status.full {{ background-color: #d4edda; color: #155724; }}
            .status.limited {{ background-color: #fff3cd; color: #856404; }}
            .chat-container {{
                border: 1px solid #ddd;
                border-radius: 5px;
                height: 500px;
                overflow-y: auto;
                padding: 15px;
                margin-bottom: 20px;
                background-color: #fafafa;
            }}
            .message {{
                margin-bottom: 15px;
                padding: 12px;
                border-radius: 8px;
                max-width: 80%;
            }}
            .user-message {{
                background-color: #e3f2fd;
                margin-left: auto;
                text-align: right;
            }}
            .bot-message {{
                background-color: #f1f8e9;
                margin-right: auto;
            }}
            .message-content {{
                white-space: pre-wrap;
                word-wrap: break-word;
            }}
            .input-container {{
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }}
            #messageInput {{
                flex: 1;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                resize: vertical;
                min-height: 50px;
            }}
            #sendButton {{
                padding: 12px 24px;
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                align-self: flex-end;
            }}
            #sendButton:hover {{
                background-color: #1976d2;
            }}
            #sendButton:disabled {{
                background-color: #ccc;
                cursor: not-allowed;
            }}
            .examples {{
                margin-top: 10px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 5px;
                font-size: 14px;
            }}
            .example-button {{
                display: inline-block;
                margin: 2px;
                padding: 5px 10px;
                background-color: #e9ecef;
                border: 1px solid #ced4da;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
            }}
            .example-button:hover {{
                background-color: #dee2e6;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 OpenManus Full Web Interface</h1>
            <div class="status {'full' if OPENMANUS_AVAILABLE else 'limited'}">
                Status: {status_text} (Running on port 9999)
            </div>
            
            <div id="chatContainer" class="chat-container">
                <div class="message bot-message">
                    <strong>OpenManus:</strong>
                    <div class="message-content">Hello! I'm OpenManus, your AI assistant. I can help you with various tasks including:

• Code analysis and generation
• Web browsing and data extraction  
• File operations and text editing
• System commands execution
• Data analysis and visualization

{'Try asking me to perform specific tasks!' if OPENMANUS_AVAILABLE else 'Note: Some features may be limited due to missing dependencies.'}

What would you like me to help you with today?</div>
                </div>
            </div>
            
            <div class="input-container">
                <textarea id="messageInput" placeholder="Type your message or task here... (Press Ctrl+Enter to send)"></textarea>
                <button id="sendButton" onclick="sendMessage()">Send</button>
            </div>
            
            <div class="examples">
                <strong>Example tasks you can try:</strong><br>
                <span class="example-button" onclick="setMessage('Help me analyze this code')">Analyze Code</span>
                <span class="example-button" onclick="setMessage('Create a Python script to sort a list')">Generate Code</span>
                <span class="example-button" onclick="setMessage('What is the current time?')">Get Time</span>
                <span class="example-button" onclick="setMessage('Help me understand how to use OpenManus')">Get Help</span>
            </div>
        </div>

        <script>
            let sessionId = Math.random().toString(36).substring(7);
            
            function addMessage(content, isUser = false) {{
                const chatContainer = document.getElementById('chatContainer');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${{isUser ? 'user-message' : 'bot-message'}}`;
                messageDiv.innerHTML = `<strong>${{isUser ? 'You' : 'OpenManus'}}:</strong><div class="message-content">${{content}}</div>`;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }}
            
            function setMessage(text) {{
                document.getElementById('messageInput').value = text;
            }}
            
            async function sendMessage() {{
                const input = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');
                const message = input.value.trim();
                
                if (!message) return;
                
                // Add user message to chat
                addMessage(message, true);
                input.value = '';
                sendButton.disabled = true;
                sendButton.textContent = 'Processing...';
                
                try {{
                    const response = await fetch('/chat', {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/json',
                        }},
                        body: JSON.stringify({{
                            message: message,
                            session_id: sessionId
                        }})
                    }});
                    
                    const data = await response.json();
                    
                    if (data.status === 'success') {{
                        addMessage(data.response);
                    }} else {{
                        addMessage(`Error: ${{data.error || 'Unknown error occurred'}}`, false);
                    }}
                }} catch (error) {{
                    console.error('Error:', error);
                    addMessage('Sorry, there was a connection error.', false);
                }}
                
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }}
            
            // Allow Ctrl+Enter to send message
            document.getElementById('messageInput').addEventListener('keydown', function(e) {{
                if (e.ctrlKey && e.key === 'Enter') {{
                    sendMessage();
                }}
            }});
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Handle chat requests with full OpenManus integration."""
    try:
        session_id = request.session_id or "default"
        
        # Try to use full OpenManus functionality
        if OPENMANUS_AVAILABLE:
            try:
                response_text = await execute_with_manus(request.message, session_id)
            except Exception as e:
                logger.error(f"Manus execution failed: {e}")
                response_text = await execute_simple_response(request.message, session_id)
        else:
            response_text = await execute_simple_response(request.message, session_id)
        
        return ChatResponse(
            response=response_text,
            session_id=session_id,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        return ChatResponse(
            response="Sorry, I encountered an error while processing your request.",
            session_id=request.session_id or "default",
            status="error",
            error=str(e)
        )

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy", 
        "service": "OpenManus Full Web Interface",
        "openmanus_available": OPENMANUS_AVAILABLE
    }

@app.get("/api/info")
async def api_info():
    """API information endpoint."""
    return {
        "name": "OpenManus Full Web Interface",
        "version": "2.0.0",
        "description": "A complete web interface for the OpenManus AI agent with task execution capabilities",
        "openmanus_integration": OPENMANUS_AVAILABLE,
        "endpoints": {
            "/": "Main web interface",
            "/chat": "Chat with the agent (POST)",
            "/health": "Health check",
            "/api/info": "API information"
        }
    }

def main():
    """Main function to start the web server."""
    parser = argparse.ArgumentParser(description="OpenManus Full Web Server")
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Server host address (default: localhost)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=9999,
        help="Server port (default: 9999)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    args = parser.parse_args()
    
    logger.info(f"Starting OpenManus Full Web Server on {args.host}:{args.port}")
    logger.info(f"OpenManus Integration: {'Available' if OPENMANUS_AVAILABLE else 'Limited'}")
    logger.info(f"Open your browser and go to: http://{args.host}:{args.port}")
    
    # Start the server
    uvicorn.run(
        "full_web_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
