# Global LLM configuration
[llm]
model = "claude-3-7-sonnet-20250219"       # The LLM model to use
base_url = "https://api.anthropic.com/v1/" # API endpoint URL
api_key = "YOUR_API_KEY"                   # Your API key - Please replace with your actual API key
max_tokens = 8192                          # Maximum number of tokens in the response
temperature = 0.0                          # Controls randomness

# Optional configuration for specific LLM models
[llm.vision]
model = "claude-3-7-sonnet-20250219"       # The vision model to use
base_url = "https://api.anthropic.com/v1/" # API endpoint URL for vision model
api_key = "YOUR_API_KEY"                   # Your API key for vision model - Please replace with your actual API key
max_tokens = 8192                          # Maximum number of tokens in the response
temperature = 0.0                          # Controls randomness for vision model

# Optional configuration for specific browser configuration
[browser]
# Whether to run browser in headless mode (default: false)
headless = false
# Disable browser security features (default: true)
disable_security = true
# Extra arguments to pass to the browser
extra_chromium_args = []

# Optional configuration, Search settings.
[search]
# Search engine for agent to use. Default is "Google", can be set to "Baidu" or "DuckDuckGo" or "Bing".
engine = "Google"
# Fallback engine order. Default is ["DuckDuckGo", "Baidu", "Bing"] - will try in this order after primary engine fails.
fallback_engines = ["DuckDuckGo", "Baidu", "Bing"]
# Seconds to wait before retrying all engines again when they all fail due to rate limits. Default is 60.
retry_delay = 60
# Maximum number of times to retry all engines when all fail. Default is 3.
max_retries = 3
# Language code for search results. Options: "en" (English), "zh" (Chinese), etc.
lang = "en"
# Country code for search results. Options: "us" (United States), "cn" (China), etc.
country = "us"

# Sandbox configuration
[sandbox]
use_sandbox = false
image = "python:3.12-slim"
work_dir = "/workspace"
memory_limit = "512m"
cpu_limit = 1.0
timeout = 300
network_enabled = false

# MCP (Model Context Protocol) configuration
[mcp]
server_reference = "app.mcp.server" # default server module reference

# Optional Runflow configuration
# Your can add additional agents into run-flow workflow to solve different-type tasks.
[runflow]
use_data_analysis_agent = false     # The Data Analysis Agent to solve various data analysis tasks
