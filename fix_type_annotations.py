#!/usr/bin/env python3
"""
Fix Python 3.8 compatibility issues with type annotations.
Union[Replace, union] syntax with Union from typing module.
"""

import os
import re
from pathlib import Path

def fix_file(file_path):
    """Fix type annotations in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Check if Union is already imported
        has_union_import = 'from typing import' in content and 'Union' in content
        
        # Find all type annotations Union[with, syntax]
        union_pattern = r'(\w+)\s*\|\s*(\w+)'
        matches = re.findall(union_pattern, content)
        
        if matches:
            print(f"Processing {file_path}")
            
            # Add Union import if not present
            if not has_union_import:
                # Find existing typing imports
                typing_import_pattern = r'from typing import ([^\n]+)'
                typing_match = re.search(typing_import_pattern, content)
                
                if typing_match:
                    # Add Union to existing import
                    existing_imports = typing_match.group(1)
                    if 'Union' not in existing_imports:
                        new_imports = existing_imports.rstrip() + ', Union'
                        content = content.replace(
                            f'from typing import {existing_imports}',
                            f'from typing import {new_imports}'
                        )
                else:
                    # Add new typing import
                    content = 'from typing import Union\n' + content
            
            # Union[Replace, syntax] with Union
            content = re.sub(
                r'(\w+)\s*\|\s*(\w+)',
                r'Union[\1, \2]',
                content
            )
            
            # Write back if changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Fixed {file_path}")
                return True
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
    
    return False

def main():
    """Main function to fix all Python files."""
    project_root = Path('.')
    python_files = list(project_root.rglob('*.py'))
    
    fixed_count = 0
    for py_file in python_files:
        if fix_file(py_file):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files")

if __name__ == '__main__':
    main()
