#!/usr/bin/env python3
"""
Simple Web Server for OpenManus
A basic FastAPI server that provides a web interface for the OpenManus agent.
"""

import argparse
import asyncio
import json
import logging
from typing import Dict, Any, Optional

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse
    from pydantic import BaseModel
    import uvicorn
except ImportError:
    print("FastAPI and uvicorn are required. Please install them:")
    print("pip install fastapi uvicorn")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    status: str = "success"

# Create FastAPI app
app = FastAPI(
    title="OpenManus Web Interface",
    description="A web interface for the OpenManus AI agent",
    version="1.0.0"
)

# Simple in-memory session storage
sessions: Dict[str, Dict[str, Any]] = {}

@app.get("/", response_class=HTMLResponse)
async def home():
    """Serve the main web interface."""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>OpenManus Web Interface</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .chat-container {
                border: 1px solid #ddd;
                border-radius: 5px;
                height: 400px;
                overflow-y: auto;
                padding: 15px;
                margin-bottom: 20px;
                background-color: #fafafa;
            }
            .message {
                margin-bottom: 15px;
                padding: 10px;
                border-radius: 5px;
            }
            .user-message {
                background-color: #e3f2fd;
                text-align: right;
            }
            .bot-message {
                background-color: #f1f8e9;
            }
            .input-container {
                display: flex;
                gap: 10px;
            }
            #messageInput {
                flex: 1;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }
            #sendButton {
                padding: 10px 20px;
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            }
            #sendButton:hover {
                background-color: #1976d2;
            }
            #sendButton:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
            .status {
                text-align: center;
                margin: 10px 0;
                font-style: italic;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 OpenManus Web Interface</h1>
            <div class="status">Ready to chat! (Running on port 9999)</div>
            <div id="chatContainer" class="chat-container">
                <div class="message bot-message">
                    <strong>OpenManus:</strong> Hello! I'm OpenManus, your AI assistant. How can I help you today?
                </div>
            </div>
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Type your message here..." />
                <button id="sendButton" onclick="sendMessage()">Send</button>
            </div>
        </div>

        <script>
            let sessionId = Math.random().toString(36).substring(7);
            
            function addMessage(content, isUser = false) {
                const chatContainer = document.getElementById('chatContainer');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
                messageDiv.innerHTML = `<strong>${isUser ? 'You' : 'OpenManus'}:</strong> ${content}`;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');
                const message = input.value.trim();
                
                if (!message) return;
                
                // Add user message to chat
                addMessage(message, true);
                input.value = '';
                sendButton.disabled = true;
                sendButton.textContent = 'Sending...';
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            session_id: sessionId
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.status === 'success') {
                        addMessage(data.response);
                    } else {
                        addMessage('Sorry, there was an error processing your request.', false);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    addMessage('Sorry, there was a connection error.', false);
                }
                
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }
            
            // Allow Enter key to send message
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Handle chat requests."""
    try:
        # For now, return a simple response since we can't run the full agent
        # In a full implementation, this would call the Manus agent
        
        session_id = request.session_id or "default"
        
        # Simple echo response for demonstration
        response_text = f"I received your message: '{request.message}'. "
        response_text += "Note: This is a simplified version running without full dependencies. "
        response_text += "To use the full OpenManus capabilities, please install all required dependencies."
        
        return ChatResponse(
            response=response_text,
            session_id=session_id,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "OpenManus Web Interface"}

@app.get("/api/info")
async def api_info():
    """API information endpoint."""
    return {
        "name": "OpenManus Web Interface",
        "version": "1.0.0",
        "description": "A web interface for the OpenManus AI agent",
        "endpoints": {
            "/": "Main web interface",
            "/chat": "Chat with the agent (POST)",
            "/health": "Health check",
            "/api/info": "API information"
        }
    }

def main():
    """Main function to start the web server."""
    parser = argparse.ArgumentParser(description="OpenManus Web Server")
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Server host address (default: localhost)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=9999,
        help="Server port (default: 9999)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    args = parser.parse_args()
    
    logger.info(f"Starting OpenManus Web Server on {args.host}:{args.port}")
    logger.info(f"Open your browser and go to: http://{args.host}:{args.port}")
    
    # Start the server
    uvicorn.run(
        "simple_web_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
