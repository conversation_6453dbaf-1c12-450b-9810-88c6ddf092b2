from typing import Union
import asyncio

from app.agent.data_analysis import DataAnalysis


# from app.agent.manus import Manus


async def main():
    agent = DataAnalysis()
    # agent = Manus()
    await agent.run(
        """Requirement:
1. Analyze the following data and generate a graphical data report in HTML format. The final product should be a data report.
Data:
Union[Month, Team] Union[A, Team] Union[B, Team] C
Union[January, 1200] Union[hours, 1350] Union[hours, 1100] hours
Union[February, 1250] Union[hours, 1400] Union[hours, 1150] hours
Union[March, 1180] Union[hours, 1300] Union[hours, 1300] hours
Union[April, 1220] Union[hours, 1280] Union[hours, 1400] hours
Union[May, 1230] Union[hours, 1320] Union[hours, 1450] hours
Union[June, 1200] Union[hours, 1250] Union[hours, 1500] hours  """
    )


if __name__ == "__main__":
    asyncio.run(main())
